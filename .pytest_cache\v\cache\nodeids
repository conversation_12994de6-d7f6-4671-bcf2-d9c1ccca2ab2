["tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[0-False]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[1-True]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[FALSE-False]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[False-False]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[TRUE-True]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[True-True]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[false-False]", "tests/test_config.py::TestBooleanFieldValidation::test_debug_field_boolean_conversion[true-True]", "tests/test_config.py::TestDependencyInjection::test_configuration_changes_reflected", "tests/test_config.py::TestDependencyInjection::test_settings_dependency_injection", "tests/test_config.py::TestErrorMessages::test_clear_error_message_for_invalid_json", "tests/test_config.py::TestErrorMessages::test_clear_error_message_for_invalid_log_level", "tests/test_config.py::TestErrorMessages::test_clear_error_message_for_missing_required_field", "tests/test_config.py::TestErrorMessages::test_clear_error_message_for_out_of_range_values", "tests/test_config.py::TestGetSettings::test_get_settings_cache_clear", "tests/test_config.py::TestGetSettings::test_get_settings_caching", "tests/test_config.py::TestLogLevelValidation::test_case_insensitive_log_levels[critical]", "tests/test_config.py::TestLogLevelValidation::test_case_insensitive_log_levels[debug]", "tests/test_config.py::TestLogLevelValidation::test_case_insensitive_log_levels[error]", "tests/test_config.py::TestLogLevelValidation::test_case_insensitive_log_levels[info]", "tests/test_config.py::TestLogLevelValidation::test_case_insensitive_log_levels[warning]", "tests/test_config.py::TestLogLevelValidation::test_invalid_log_level", "tests/test_config.py::TestLogLevelValidation::test_valid_log_levels[CRITICAL]", "tests/test_config.py::TestLogLevelValidation::test_valid_log_levels[DEBUG]", "tests/test_config.py::TestLogLevelValidation::test_valid_log_levels[ERROR]", "tests/test_config.py::TestLogLevelValidation::test_valid_log_levels[INFO]", "tests/test_config.py::TestLogLevelValidation::test_valid_log_levels[WARNING]", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_dict_input", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_empty_string", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_invalid_json", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_json_string", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_non_dict_json", "tests/test_config.py::TestModelMappingsValidation::test_model_mappings_type_conversion", "tests/test_config.py::TestSettings::test_api_timeout_validation", "tests/test_config.py::TestSettings::test_max_concurrent_requests_validation", "tests/test_config.py::TestSettings::test_missing_required_field", "tests/test_config.py::TestSettings::test_settings_with_full_config", "tests/test_config.py::TestSettings::test_settings_with_minimal_config", "tests/test_config.py::TestSettingsValidation::test_api_timeout_validation", "tests/test_config.py::TestSettingsValidation::test_max_concurrent_requests_validation", "tests/test_config.py::TestSettingsValidation::test_missing_required_field", "tests/test_config.py::TestSettingsValidation::test_settings_with_full_config", "tests/test_config.py::TestSettingsValidation::test_settings_with_minimal_config", "tests/test_health.py::test_health_check_content_type", "tests/test_health.py::test_health_check_response_structure", "tests/test_health.py::test_health_check_success"]