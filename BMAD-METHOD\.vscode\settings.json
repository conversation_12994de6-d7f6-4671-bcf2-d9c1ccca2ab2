{"cSpell.words": ["agentic", "A<PERSON>os", "BMAD", "Centricity", "dataclass", "docstrings", "emergently", "explorative", "frontends", "golint", "Goroutines", "HSTS", "httpx", "Immer", "implementability", "Inclusivity", "<PERSON><PERSON>", "pasteable", "Pi<PERSON>", "Polyre<PERSON>", "Pydantic", "pyproject", "rescope", "roadmaps", "roleplay", "runbooks", "Serilog", "shadcn", "structlog", "Systemization", "taskroot", "Testcontainers", "tmpl", "VARCHAR", "venv", "WCAG"]}