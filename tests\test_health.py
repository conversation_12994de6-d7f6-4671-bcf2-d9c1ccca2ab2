"""
Tests for health check endpoint.

This module contains unit and integration tests for the health check
functionality to ensure proper service monitoring.
"""

import pytest
from fastapi.testclient import TestClient
from datetime import datetime

from app.main import app


@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)


def test_health_check_success(client):
    """Test successful health check response."""
    response = client.get("/health")
    
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "ollama-bridge"
    assert data["version"] == "0.1.0"
    assert "timestamp" in data
    assert "backend_url" in data
    assert "debug_mode" in data
    
    # Verify timestamp is recent (within last minute)
    timestamp = datetime.fromisoformat(data["timestamp"].replace("Z", "+00:00"))
    now = datetime.utcnow().replace(tzinfo=timestamp.tzinfo)
    time_diff = (now - timestamp).total_seconds()
    assert time_diff < 60  # Less than 1 minute old


def test_health_check_response_structure(client):
    """Test health check response has correct structure."""
    response = client.get("/health")
    
    assert response.status_code == 200
    
    data = response.json()
    required_fields = ["status", "timestamp", "version", "service", "backend_url", "debug_mode"]
    
    for field in required_fields:
        assert field in data, f"Missing required field: {field}"


def test_health_check_content_type(client):
    """Test health check returns JSON content type."""
    response = client.get("/health")
    
    assert response.status_code == 200
    assert response.headers["content-type"] == "application/json"
